# بيئة معزولة للذكاء الاصطناعي ذاتي التطوير
FROM python:3.9-slim

# تحديث النظام وتثبيت الأدوات الأساسية
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# إنشاء مستخدم غير جذر للأمان
RUN useradd -m -s /bin/bash aiuser
USER aiuser
WORKDIR /home/<USER>

# تثبيت المكتبات الأساسية
COPY requirements.txt .
RUN pip install --user -r requirements.txt

# إنشاء مجلد العمل
RUN mkdir -p /home/<USER>/ai_workspace
WORKDIR /home/<USER>/ai_workspace

# نسخ الكود الأساسي
COPY --chown=aiuser:aiuser . .

# تعيين متغيرات البيئة
ENV PYTHONPATH=/home/<USER>/ai_workspace
ENV AI_SANDBOX=true
ENV AI_SAFETY_MODE=development

# نقطة الدخول
CMD ["python", "main.py"]
