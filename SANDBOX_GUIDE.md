# دليل البيئة المعزولة للذكاء الاصطناعي ذاتي التطوير

## 🎯 الهدف
إنشاء بيئة آمنة ومعزولة لتطوير واختبار نظام ذكاء اصطناعي قادر على تطوير نفسه.

## 🛡️ مستويات الأمان

### المستوى 1: Docker (الأكثر أماناً) ⭐⭐⭐⭐⭐
- عزل كامل عن النظام الأساسي
- قيود على الموارد
- سهولة الحذف والإعادة
- مراقبة الشبكة

### المستوى 2: محلي مع مراقبة ⭐⭐⭐
- مراقبة الأمان المستمرة
- إيقاف تلقائي عند الخطر
- تسجيل جميع الأنشطة
- قيود على العمليات الخطيرة

### المستوى 3: بدون قيود ⭐ (خطير!)
- حرية كاملة للنظام
- بدون مراقبة أو قيود
- مخاطر عالية جداً

## 🚀 طرق التشغيل

### الطريقة الأولى: استخدام المشغل التفاعلي
```bash
python run_sandbox.py
```

### الطريقة الثانية: Docker مباشرة
```bash
# إعداد سريع
./sandbox_setup.sh

# أو يدوياً
docker-compose build
docker-compose up -d
```

### الطريقة الثالثة: محلي مع مراقبة
```bash
python safety_monitor.py &
python main.py
```

## 📊 مراقبة النظام

### عرض السجلات
```bash
# سجلات Docker
docker-compose logs -f

# سجلات الأمان
tail -f logs/safety_monitor.log

# سجلات النظام
tail -f logs/ai_system.log
```

### دخول البيئة المعزولة
```bash
# دخول حاوية Docker
docker-compose exec ai-sandbox bash

# عرض حالة الموارد
docker stats self_improving_ai
```

### إيقاف النظام
```bash
# إيقاف Docker
docker-compose down

# إيقاف طارئ
docker kill self_improving_ai

# حذف كامل
docker-compose down -v
docker rmi $(docker images -q)
```

## 🔧 التكوين والإعدادات

### ملف docker-compose.yml
- قيود الموارد: 4GB RAM, 2 CPU cores
- شبكة معزولة
- مجلدات مشتركة محدودة

### ملف safety_monitor.py
- مراقبة استخدام الموارد
- كشف العمليات المشبوهة
- مراقبة النشاط الشبكي
- حماية الملفات المهمة

## 📁 هيكل المجلدات

```
project-n/
├── Dockerfile                 # تعريف الحاوية
├── docker-compose.yml         # تكوين الخدمات
├── requirements.txt           # المتطلبات
├── run_sandbox.py            # المشغل الرئيسي
├── safety_monitor.py         # مراقب الأمان
├── sandbox_setup.sh          # إعداد سريع
├── ai_workspace/             # مساحة عمل الذكاء الاصطناعي
├── logs/                     # ملفات السجلات
├── data/                     # البيانات
└── backups/                  # النسخ الاحتياطية
```

## ⚠️ تحذيرات مهمة

### قبل التشغيل:
1. **احفظ نسخة احتياطية** من بياناتك المهمة
2. **استخدم جهاز منفصل** إذا أمكن
3. **راقب النظام باستمرار** أثناء التشغيل
4. **اقرأ السجلات** بانتظام

### علامات الخطر:
- استخدام مرتفع للموارد (>80%)
- عمليات مشبوهة (rm, del, format)
- اتصالات شبكية غير متوقعة
- تعديل ملفات النظام

### في حالة الطوارئ:
```bash
# إيقاف فوري
docker kill self_improving_ai
pkill -f "python.*main.py"

# حذف كامل
docker-compose down -v
rm -rf ai_workspace/* logs/* data/*
```

## 🔍 استكشاف الأخطاء

### مشاكل Docker الشائعة:
```bash
# تنظيف Docker
docker system prune -a

# إعادة بناء الحاوية
docker-compose build --no-cache

# فحص الموارد
docker system df
```

### مشاكل الأذونات:
```bash
# إصلاح الأذونات
sudo chown -R $USER:$USER ai_workspace/
chmod +x *.sh *.py
```

### مشاكل الذاكرة:
```bash
# زيادة حد الذاكرة في docker-compose.yml
memory: 8G  # بدلاً من 4G

# مراقبة الاستخدام
free -h
htop
```

## 📞 الدعم والمساعدة

إذا واجهت مشاكل:
1. تحقق من السجلات أولاً
2. راجع هذا الدليل
3. جرب إعادة التشغيل
4. في الحالات الطارئة: أوقف كل شيء

## 🎓 نصائح للمطورين

1. **ابدأ بسيط**: اختبر مكونات صغيرة أولاً
2. **راقب باستمرار**: لا تترك النظام يعمل بدون مراقبة
3. **احفظ نسخ احتياطية**: قبل كل تجربة جديدة
4. **وثق كل شيء**: سجل ما تفعله وما يحدث
5. **كن حذراً**: الذكاء الاصطناعي ذاتي التطوير قوي جداً

---

**تذكر**: هذا نظام تجريبي قوي. استخدمه بحذر ومسؤولية! 🚨
