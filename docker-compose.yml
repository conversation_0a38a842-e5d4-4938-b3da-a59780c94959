version: '3.8'

services:
  ai-sandbox:
    build: .
    container_name: self_improving_ai
    
    # قيود الموارد للأمان
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # قيود الشبكة
    networks:
      - ai_network
    
    # المجلدات المشتركة
    volumes:
      - ./ai_workspace:/home/<USER>/ai_workspace
      - ./logs:/home/<USER>/logs
      - ./data:/home/<USER>/data
    
    # متغيرات البيئة
    environment:
      - AI_SANDBOX=true
      - AI_SAFETY_MODE=development
      - PYTHONUNBUFFERED=1
    
    # إعادة التشغيل التلقائي
    restart: unless-stopped
    
    # منافذ الوصول
    ports:
      - "8888:8888"  # Jupyter
      - "5000:5000"  # Flask API
    
    # الأوامر الأولية
    command: >
      bash -c "
        echo 'بدء تشغيل نظام الذكاء الاصطناعي ذاتي التطوير...'
        python main.py
      "

networks:
  ai_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  ai_data:
  ai_logs:
