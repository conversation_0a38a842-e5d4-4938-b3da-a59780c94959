#!/usr/bin/env python3
"""
مشغل البيئة المعزولة للذكاء الاصطناعي ذاتي التطوير
"""

import os
import sys
import subprocess
import time
from safety_monitor import SafetyMonitor

def check_docker():
    """التحقق من وجود Docker"""
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker متوفر")
            return True
        else:
            print("❌ Docker غير متوفر")
            return False
    except FileNotFoundError:
        print("❌ Docker غير مثبت")
        return False

def setup_environment():
    """إعداد البيئة"""
    print("🔧 إعداد البيئة المعزولة...")
    
    # إنشاء المجلدات المطلوبة
    directories = ['ai_workspace', 'logs', 'data', 'backups']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 تم إنشاء مجلد: {directory}")
    
    # إنشاء ملف requirements.txt إذا لم يكن موجود
    if not os.path.exists('requirements.txt'):
        requirements = """numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
tensorflow>=2.8.0
torch>=1.11.0
transformers>=4.20.0
flask>=2.0.0
requests>=2.28.0
beautifulsoup4>=4.11.0
selenium>=4.0.0
jupyter>=1.0.0
matplotlib>=3.5.0
seaborn>=0.11.0
pyyaml>=6.0
psutil>=5.9.0
gitpython>=3.1.0
openai>=0.27.0
anthropic>=0.3.0"""
        
        with open('requirements.txt', 'w') as f:
            f.write(requirements)
        print("📦 تم إنشاء ملف requirements.txt")

def run_with_docker():
    """تشغيل النظام باستخدام Docker"""
    print("🐳 تشغيل النظام في حاوية Docker...")
    
    try:
        # بناء الحاوية
        print("🔨 بناء الحاوية...")
        build_result = subprocess.run(['docker-compose', 'build'], 
                                    capture_output=True, text=True)
        if build_result.returncode != 0:
            print(f"❌ خطأ في بناء الحاوية: {build_result.stderr}")
            return False
        
        # تشغيل الحاوية
        print("🚀 تشغيل الحاوية...")
        run_result = subprocess.run(['docker-compose', 'up', '-d'], 
                                  capture_output=True, text=True)
        if run_result.returncode != 0:
            print(f"❌ خطأ في تشغيل الحاوية: {run_result.stderr}")
            return False
        
        print("✅ تم تشغيل النظام في البيئة المعزولة")
        print("\n📋 الأوامر المفيدة:")
        print("  عرض السجلات:     docker-compose logs -f")
        print("  دخول الحاوية:    docker-compose exec ai-sandbox bash")
        print("  إيقاف النظام:    docker-compose down")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل Docker: {e}")
        return False

def run_local():
    """تشغيل النظام محلياً مع المراقبة"""
    print("💻 تشغيل النظام محلياً مع مراقبة الأمان...")
    
    # بدء مراقب الأمان
    monitor = SafetyMonitor()
    monitor.start_monitoring()
    
    try:
        # تشغيل النظام الرئيسي
        print("🚀 بدء تشغيل النظام...")
        
        # هنا سيتم استدعاء النظام الرئيسي
        # subprocess.run([sys.executable, 'main.py'])
        
        print("⚠️ النظام الرئيسي لم يتم تطويره بعد")
        print("🛡️ مراقب الأمان يعمل...")
        
        # حلقة المراقبة
        while True:
            time.sleep(10)
            status = monitor.get_status()
            print(f"📊 الموارد: CPU {status['system_resources']['cpu_percent']:.1f}% | "
                  f"RAM {status['system_resources']['memory_percent']:.1f}% | "
                  f"تنبيهات: {status['alerts_count']}")
            
    except KeyboardInterrupt:
        print("\n🛑 إيقاف النظام...")
        monitor.stop_monitoring()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        monitor.stop_monitoring()

def main():
    """الدالة الرئيسية"""
    print("🤖 مرحباً بك في نظام الذكاء الاصطناعي ذاتي التطوير")
    print("=" * 60)
    
    # إعداد البيئة
    setup_environment()
    
    # اختيار طريقة التشغيل
    print("\nاختر طريقة التشغيل:")
    print("1. Docker (الأكثر أماناً)")
    print("2. محلي مع مراقبة الأمان")
    print("3. محلي بدون قيود (خطير!)")
    
    choice = input("\nاختيارك (1-3): ").strip()
    
    if choice == "1":
        if check_docker():
            if run_with_docker():
                print("\n🎉 تم تشغيل النظام بنجاح في البيئة المعزولة!")
            else:
                print("\n❌ فشل في تشغيل النظام")
        else:
            print("\n❌ يرجى تثبيت Docker أولاً")
            
    elif choice == "2":
        print("\n⚠️ تحذير: التشغيل المحلي مع مراقبة الأمان")
        confirm = input("هل أنت متأكد؟ (yes/no): ").lower()
        if confirm in ['yes', 'y', 'نعم']:
            run_local()
        else:
            print("تم الإلغاء")
            
    elif choice == "3":
        print("\n🚨 تحذير خطير: التشغيل بدون قيود!")
        print("هذا قد يضر بنظامك!")
        confirm = input("اكتب 'أفهم المخاطر' للمتابعة: ")
        if confirm == "أفهم المخاطر":
            print("🔥 تشغيل بدون قيود... حظاً موفقاً!")
            # هنا يمكن تشغيل النظام بدون أي قيود
            print("⚠️ هذا الوضع لم يتم تطويره بعد لأسباب أمنية")
        else:
            print("تم الإلغاء - قرار حكيم!")
    else:
        print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    main()
