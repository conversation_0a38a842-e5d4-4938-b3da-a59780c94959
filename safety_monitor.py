#!/usr/bin/env python3
"""
نظام مراقبة الأمان للذكاء الاصطناعي ذاتي التطوير
يراقب النشاط ويمكنه إيقاف النظام في حالة الخطر
"""

import os
import time
import psutil
import threading
import logging
from datetime import datetime
from typing import Dict, List, Any

class SafetyMonitor:
    def __init__(self):
        self.is_monitoring = False
        self.alerts = []
        self.resource_limits = {
            'cpu_percent': 80,
            'memory_percent': 80,
            'disk_usage_percent': 90,
            'network_connections': 100
        }
        
        # إعداد التسجيل
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/safety_monitor.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def start_monitoring(self):
        """بدء مراقبة النظام"""
        self.is_monitoring = True
        self.logger.info("🛡️ بدء مراقبة الأمان")
        
        # خيوط المراقبة المختلفة
        threading.Thread(target=self._monitor_resources, daemon=True).start()
        threading.Thread(target=self._monitor_processes, daemon=True).start()
        threading.Thread(target=self._monitor_network, daemon=True).start()
        threading.Thread(target=self._monitor_files, daemon=True).start()
        
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.is_monitoring = False
        self.logger.info("🛑 إيقاف مراقبة الأمان")
        
    def _monitor_resources(self):
        """مراقبة استخدام الموارد"""
        while self.is_monitoring:
            try:
                # مراقبة المعالج
                cpu_percent = psutil.cpu_percent(interval=1)
                if cpu_percent > self.resource_limits['cpu_percent']:
                    self._trigger_alert(f"استخدام المعالج مرتفع: {cpu_percent}%")
                
                # مراقبة الذاكرة
                memory = psutil.virtual_memory()
                if memory.percent > self.resource_limits['memory_percent']:
                    self._trigger_alert(f"استخدام الذاكرة مرتفع: {memory.percent}%")
                
                # مراقبة القرص
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                if disk_percent > self.resource_limits['disk_usage_percent']:
                    self._trigger_alert(f"استخدام القرص مرتفع: {disk_percent:.1f}%")
                    
            except Exception as e:
                self.logger.error(f"خطأ في مراقبة الموارد: {e}")
                
            time.sleep(5)
    
    def _monitor_processes(self):
        """مراقبة العمليات الجارية"""
        suspicious_processes = ['rm', 'del', 'format', 'fdisk', 'mkfs']
        
        while self.is_monitoring:
            try:
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] in suspicious_processes:
                            self._trigger_alert(f"عملية مشبوهة: {proc.info['name']}")
                            
                        # فحص سطر الأوامر للأوامر الخطيرة
                        if proc.info['cmdline']:
                            cmdline = ' '.join(proc.info['cmdline'])
                            if any(dangerous in cmdline.lower() for dangerous in 
                                  ['rm -rf', 'del /f', 'format c:', 'sudo rm']):
                                self._trigger_alert(f"أمر خطير: {cmdline}")
                                
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                        
            except Exception as e:
                self.logger.error(f"خطأ في مراقبة العمليات: {e}")
                
            time.sleep(10)
    
    def _monitor_network(self):
        """مراقبة النشاط الشبكي"""
        while self.is_monitoring:
            try:
                connections = psutil.net_connections()
                active_connections = len([c for c in connections if c.status == 'ESTABLISHED'])
                
                if active_connections > self.resource_limits['network_connections']:
                    self._trigger_alert(f"عدد اتصالات شبكية مرتفع: {active_connections}")
                
                # مراقبة استخدام البيانات
                net_io = psutil.net_io_counters()
                self.logger.debug(f"البيانات المرسلة: {net_io.bytes_sent}, المستقبلة: {net_io.bytes_recv}")
                
            except Exception as e:
                self.logger.error(f"خطأ في مراقبة الشبكة: {e}")
                
            time.sleep(15)
    
    def _monitor_files(self):
        """مراقبة تغييرات الملفات المهمة"""
        important_files = ['/etc/passwd', '/etc/shadow', '/etc/hosts']
        file_checksums = {}
        
        # حساب المجاميع الأولية
        for file_path in important_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'rb') as f:
                        import hashlib
                        file_checksums[file_path] = hashlib.md5(f.read()).hexdigest()
                except:
                    pass
        
        while self.is_monitoring:
            try:
                for file_path in important_files:
                    if os.path.exists(file_path):
                        try:
                            with open(file_path, 'rb') as f:
                                import hashlib
                                current_checksum = hashlib.md5(f.read()).hexdigest()
                                
                            if file_path in file_checksums:
                                if file_checksums[file_path] != current_checksum:
                                    self._trigger_alert(f"تم تعديل ملف مهم: {file_path}")
                                    file_checksums[file_path] = current_checksum
                        except:
                            pass
                            
            except Exception as e:
                self.logger.error(f"خطأ في مراقبة الملفات: {e}")
                
            time.sleep(30)
    
    def _trigger_alert(self, message: str):
        """إطلاق تنبيه أمني"""
        alert = {
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'severity': 'HIGH'
        }
        
        self.alerts.append(alert)
        self.logger.warning(f"🚨 تنبيه أمني: {message}")
        
        # إذا كان التنبيه خطير جداً، أوقف النظام
        critical_keywords = ['rm -rf', 'format', 'del /f', 'sudo rm']
        if any(keyword in message.lower() for keyword in critical_keywords):
            self.logger.critical("🛑 إيقاف طارئ للنظام!")
            self._emergency_shutdown()
    
    def _emergency_shutdown(self):
        """إيقاف طارئ للنظام"""
        try:
            # حفظ حالة النظام
            self._save_emergency_state()
            
            # إيقاف العمليات الخطيرة
            os.system("pkill -f 'python.*main.py'")
            
            self.logger.critical("تم الإيقاف الطارئ للنظام")
            
        except Exception as e:
            self.logger.error(f"خطأ في الإيقاف الطارئ: {e}")
    
    def _save_emergency_state(self):
        """حفظ حالة النظام في حالة الطوارئ"""
        try:
            state = {
                'timestamp': datetime.now().isoformat(),
                'alerts': self.alerts,
                'system_info': {
                    'cpu_percent': psutil.cpu_percent(),
                    'memory_percent': psutil.virtual_memory().percent,
                    'processes': len(psutil.pids())
                }
            }
            
            import json
            with open('logs/emergency_state.json', 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"خطأ في حفظ حالة الطوارئ: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """الحصول على حالة المراقبة"""
        return {
            'is_monitoring': self.is_monitoring,
            'alerts_count': len(self.alerts),
            'recent_alerts': self.alerts[-5:] if self.alerts else [],
            'system_resources': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': (psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100
            }
        }

if __name__ == "__main__":
    monitor = SafetyMonitor()
    monitor.start_monitoring()
    
    try:
        while True:
            time.sleep(60)
            status = monitor.get_status()
            print(f"📊 حالة النظام: {status['system_resources']}")
            
    except KeyboardInterrupt:
        print("\n🛑 إيقاف المراقبة...")
        monitor.stop_monitoring()
