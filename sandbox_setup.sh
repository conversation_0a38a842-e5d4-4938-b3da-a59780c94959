#!/bin/bash

echo "🚀 إعداد بيئة الذكاء الاصطناعي ذاتي التطوير"
echo "================================================"

# التحقق من وجود Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت. يرجى تثبيت Docker أولاً"
    echo "تعليمات التثبيت: https://docs.docker.com/get-docker/"
    exit 1
fi

# التحقق من وجود Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose غير مثبت"
    exit 1
fi

echo "✅ Docker متوفر"

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات..."
mkdir -p ai_workspace
mkdir -p logs
mkdir -p data
mkdir -p backups

# إنشاء ملف requirements.txt إذا لم يكن موجود
if [ ! -f requirements.txt ]; then
    echo "📦 إنشاء ملف المتطلبات..."
    cat > requirements.txt << EOF
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
tensorflow>=2.8.0
torch>=1.11.0
transformers>=4.20.0
flask>=2.0.0
requests>=2.28.0
beautifulsoup4>=4.11.0
selenium>=4.0.0
jupyter>=1.0.0
matplotlib>=3.5.0
seaborn>=0.11.0
pyyaml>=6.0
psutil>=5.9.0
gitpython>=3.1.0
openai>=0.27.0
anthropic>=0.3.0
EOF
fi

# بناء الحاوية
echo "🔨 بناء حاوية Docker..."
docker-compose build

# تشغيل النظام
echo "🚀 تشغيل النظام في البيئة المعزولة..."
docker-compose up -d

echo ""
echo "✅ تم إعداد البيئة المعزولة بنجاح!"
echo ""
echo "📋 الأوامر المفيدة:"
echo "  عرض السجلات:     docker-compose logs -f"
echo "  دخول الحاوية:    docker-compose exec ai-sandbox bash"
echo "  إيقاف النظام:    docker-compose down"
echo "  إعادة البناء:    docker-compose build --no-cache"
echo ""
echo "🌐 الواجهات المتاحة:"
echo "  Jupyter:          http://localhost:8888"
echo "  API:              http://localhost:5000"
echo ""
echo "⚠️  تذكير: هذا نظام تجريبي قوي - راقبه باستمرار!"

# عرض حالة الحاوية
echo ""
echo "📊 حالة الحاوية:"
docker-compose ps
